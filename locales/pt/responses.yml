responses:
  common:
    unknown: "Desconhecido"
    hub: "Hub"
  setup:
    setupComplete: "Pronto para criar? Clique no botão \"Create Hub\"!"
    editMessagePrompt: "{emoji} Por favor, use o modal para editar sua mensagem."
    preview:
      titleSaved: "{emoji} Informações do hub salvadas!"
      previewTitle: "Aqui está uma prévia do seu hub:"
      name: "{emoji} Nome"
      short: "{emoji} Nome curto"
      description: "{emoji} Descrição"
    locale:
      successTitle: "Sucesso!"
      successDescription: "{tick} Sua lingua foi definida para **{locale_name}**"
    loading:
      creatingHub: "{loading} Criando seu hub..."
      pleaseWait: "Por favor, aguarde enquanto nós configuramos o espaço da sua comunidade."
    errors:
      hubCreationFailed: "{no} A criação do hub falhou"
  appeal:
    constants:
      unknownHub: "Hub desconhecido"
      noReason: "Sem motivo indicado"
    status:
      pending: "Resposta pendente"
      cooldown: "Tempo de espera"
      canAppealAgain: "Apelável"
      canAppeal: "Apelável"
    fields:
      date: "Data:"
      reason: "Motivo:"
    errors:
      recordFailed: "Falha ao escrever decisão: {error}"
      notFoundOrDeleted: "Apelo não encontrado ou deletado."
      updateFailed: "Falha ao atualizar apelo. Tente novamente mais tarde"
    dm:
      accepted: "Seu apelo em relação a uma ação de moderação{hubName} foi aceito. Nossa equipe analisou seu caso e decidiu que seu recurso deveria ser mantido."
      declined: "Seu apelo sobre uma ação moderativa em {hubName} foi avaliada. Após consideração, seu apelo foi recusado."
      moderatorNote: "Nota do moderador: {reason}"
    embed:
      title: "Suas infrações apeláveis"
      description: "Selecione uma infração abaixo para enviar um apelo, ou ver o progresso de apelos existentes."
      noInfractions:
        title: "Limpinho por aqui!"
        description: "Nenhuma infração apelável encontrada."
      footer:
        canAppeal: "💡 Você pode apelar {count} infração(ões). Use o menu abaixo."
        checkLater: "💡 Verifique mais tarde quando o tempo de espera expirar ou quando apelos forem avaliados."
  errors:
    errorTitle: "Erro!"
    interactionCheck: "Você não deve usar essa interação, pois não chamou ela."
    rateLimited: "Você está sendo limitado. Pegue um suco de maracujá."
    webhookRateLimit: "Você atingiu o limite de criação de webhooks"
    invalidInput: "Você não proveu uma resposta válida."
    invalidInvite: "Esse convite está invalido ou expirado."
    webhookError: "Falha ao criar um webhook"
    notConnected: "Eu não consegui encontrar um hub conectado nesse canal."
    noInteraction: "Esse comando somente suporta comandos slash ` / `."
    missingAppealReference: "Faltando referência do apelo."
    whoops: "Whoops! Parece que deu algo errado. Tente novamente mais tarde."
    missingArgument: "Faltando argumento: `{param}`."
    notConnectedServer: "{cross} Eu não consegui encontrar nenhuma conexão para esse servidor."
  moderation:
    permissions:
      managerRequired: "Permissões de Gerente+ necessárias."
    target:
      both: "Por favor, especifique um usuário ou um servidor, não ambos."
      missing: "Por favor, especifique um usuário ou um servidor."
    revoke:
      noActive: "Nenhum {action} ativo encontrado."
      success: "{action} revogada."
    delete:
      notImplemented: "Apagar mensagens não implementado ainda. Motivo: {reason}"
      noMessage: "Nenhuma mensagem fornecida para excluir."
      success: "A mensagem {messageId} foi excluída de todos os hubs conectados."
      notInterChatMessage: "Esta mensagem não é uma mensagem o InterChat ou já foi excluída."
      failed: "Falha ao apagar a mensagem. Tente novamente mais tarde."
      notFound: "Infração não encontrada."
    blacklist:
      permissionDenied: "Você não tem permissões de equipe do InterChat para emitir uma blacklist global."
      alreadyActive: "O alvo já tem uma blacklist global ativa"
      success: "{target} em blacklist globalmente."
    success:
      action: '{target} foi {action} {prep} {hubName}'
    errors:
      selectedHubNotFound: "Hub selecionado não encontrado."
      processingFailed: "Falha ao processar a ação moderativa: {error}"
      unknownAction: "Ação desconhecida."
      unsupportedAction: "Rota de ação não suportada."
      openPanelFailed: "Erro ao abrir painel de moderação: {error}"
      notModeratorForHub: "Você não é um moderador deste Hub."
      alreadyState: "{targetType} já está {state} neste Hub."
      invalidHubData: "Dados de hub inválidos."
      originalMessageNotFound: "Mensagem original não encontrada na base de dados."
      fetchAuthorOrServerFailed: "Não foi possível buscar o autor da mensagem ou servidor."
      hubNotFoundForMessage: "Hub não encontrado para esta mensagem."
      noModeratedHubs: "Você não tem permissão de moderação em nenhum Hub."
      noTarget: "Por favor, especifique um alvo (usuário/servidor) ou responda a uma mensagem."
  infractions:
    errors:
      noPermission: "Você não tem permissão para ver as infrações deste Hub."
      bothSelection: "Por favor, selecione apenas um usuário ou um servidor, não ambos."
      invalidServerId: "ID do servidor inválido."
    permissions:
      insufficient: "Você precisa de permissões {permission}+ neste Hub."
      managerRequired: "Permissões de Gerente+ necessárias."
    target:
      both: "Por favor, especifique um usuário ou um servidor, não ambos."
      missing: "Por favor, especifique um usuário ou um servidor."
    success:
      action: "action} {target} {prep} {hubName}."
    revoke:
      noActive: "Nenhum {action} ativo encontrado."
      success: "{action} revogada."
    delete:
      notImplemented: "Apagar mensagens não implementado ainda. Motivo: {reason}"
      notFound: "Infração não encontrada."
      success: "Infração excluída."
    blacklist:
      permissionDenied: "Você não tem permissões de equipe do InterChat para emitir uma blacklist global."
      alreadyActive: "O alvo já tem uma blacklist global ativa"
      success: "{target} em blacklist globalmente."
  report:
    errors:
      processingFailed: "Erro ao processar o reporte: {error}"
      notFoundOrDeleted: "Reporte não encontrado ou deletado."
      alreadyHandled: "Esse reporte já esta {status}"
      updateFailed: "Falha ao atualizar o reporte. Tente novamente mais tarde."
    success:
      actionPast: "Reportar {action}."
    dm:
      resolved: "Obrigado por relatar. Nossa equipe de moderação analisou-a e tomou as medidas apropriadas. Não podemos compartilhar detalhes específicos para proteger a privacidade do usuário."
  welcome:
    onGuildJoinTitle: "👋 Olá!"
    onGuildJoinDescription: |
      **Sou InterChat e tenho o prazer de estar no seu servidor em nome de nossa equipe.** 
      Juntos, podemos conectar seu servidor a outras comunidades notáveis de todo o Discord. Numerosos, potencialmente milhares de servidores localizados em um só lugar, todos cheios de pessoas ansiosas para conversar com você. 🚀 

      **Pronto para começar a construir novas pontes conosco?** 
      {dot} **Novo aqui?** O comando '/setup' fornece um guia passo a passo para ajudá-lo a começar sua jornada. 
      {dot} **Pronto para explorar?** Visite nossa [página de descoberta](https://interchat.tech/hubs) para localizar e fazer parte de nossos centros animados. 
      {dot} **Talvez algo com o qual você esteja mais familiarizado?** Experimente nosso '/call' para conexões individuais. 

      💝 **Perdido? Precisa de ajuda?** Damos as boas-vindas à nossa [comunidade de suporte](https://discord.gg/8DhUA4HNpD). Nossa equipe e nossa comunidade terão prazer em oferecer sua ajuda. Oferecemos o máximo de suporte e assistência em quaisquer problemas que você possa ter, não hesite em participar!
  staff:
    blacklist:
      notFound: "Não foi encontrada nenhuma entrada na blacklsit."
      removed: "Entrada removida da blacklist"
  user:
    achievements:
      placeholder: "Conquistas aqui"
