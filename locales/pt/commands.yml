commands:
  about:
    title: "Sobre o InterChat"
    description_text: "O InterChat conecta comunidades do Discord por interligação ativa de conversas dos servidores. Mensagens vão naturalmente entre servidores em tempo real, ajudando você a construir uma comunidade engajada em um só tópico."
    support_text: "Precisa de ajuda? Entre em nosso servidor de suporte para assistência!"
    features:
      title: "Funcionalidades"
      list: |
        - Conecte com outros servidores para conversas ativas entre servidores
        - Mensagens vao naturalmente entre servidores em tempo real
        - Ferramentas de moderaçao
    buttons:
      vote: "Vote na top.gg"
      invite: "Convide o InterChat"
      dashboard: "Abrir a Central de Comando"
      support: "Entrar no suporte"
      shardInfo: "Informações do Shard"
  setup:
    welcome:
      title: "Bem-vindo a configuração do InterChat"
      description: "Vamos ter o seu servidor conectado. Esse instalador ira te guiar a como criar ou entrar em um hub."
    options:
      create:
        label: "Criar Hub"
        description: "Comece sua própria comunidade no Interchat"
      join:
        label: "Entre em um hub"
        description: "Conecte-se a uma comunidade existente"
    create:
      whatYoullCreate:
        title: "O que você deve criar:"
        description: "{dot} Um espaço único a comunidade para seus servidores\n{dot} Controle completo sobre as regras e moderação\n{dot} Configurações e funcionalidades customizadas\n{dot} Privado por padrão - somente convites"
      youllNeed:
        title: "Você precisará:"
        description: "{arrow_right} Nome criativo para o hub\n{arrow_right} Descrição breve\n{arrow_right} Descrição detalhada\n{arrow_right} Imagem URL de logo"
    join:
      title: "Entrar em um hub"
      description: "Use um diretório público ou conecte com um código de convite."
      publicHubs:
        title: "Hubs públicos"
        description: "**Pesquise em** [interchat.tech/hubs]{https://interchat.tech/hubs} \n**Ou clique** no botão abaixo para abrir o diretório"
      privateHubs:
        title: "Privado ou Somente via convite"
        description: "**Pergunte ao criador do hub ou gerente** por um código de convite\n**Execute `/connect <código de convite>`** no seu servidor para entrar"
      footer: "Você pode entrar em mais hubs a qualquer momento, e você pode se conectar a múltiplos hubs de uma vez em diferentes canais."
    nextSteps:
      created:
        title: "Hub Criado com Sucesso!"
        description: |
          Seu hub **{hubName}** está pronto! Aqui está o que você pode fazer em seguida:
          - Convidar seu primeiro servidor usando `/invite`}
          - Customizar configurações no [painel de controle]{https://interchat.tech/dashboard}
          - Conectar canais com `/connect <nome do hub>`
        inviteLink:
          title: "Próximo: Crie links de convite"
          description: "Use {hubInviteCommand} para gerar códigos de convite para **{hubName}** e compartilhá-los com outros proprietários do servidor."
        shareHub:
          title: "Compartilhe seu Hub"
          description: "{dot} Publique sobre seu novo hub em nosso [Servidor de Suporte]({supportInvite})\n{dot} Compartilhe com amigos e comunidades\n{dot} Use mídias sociais para espalhar a palavra"
        proTips:
          title: "{dot} Dicas profissionais"
          description: "{dot} Visite o [Dashboard]({website}) para configurações avançadas\n{dot} Use {hubVisibilityCommand} para tornar seu hub público\n{dot} Junte-se ao nosso [Servidor de Suporte]({supportInvite}) para ajuda e atualizações"
        footer: "Precisa de ajuda? Entre em nosso servidor de suporte"
  report:
    title: "Reportar mensagem"
    footer: "Reportes ajudam a manter o InterChat seguro para todos"
    contextMenu: "Reportar mensagem"
    description: "Reportar {user} para a **moderação do hub** por violações no hub ou **moderação do InterChat** para violações na plataforma. O usuário não será notificado, mas moderadores podem ver quem a fez."
    success:
      title: "Reporte enviado"
      toStaff: "{tick} Seu reporte foi enviado a moderação do InterChat para avaliação."
      toHub: "{tick} Seu reporte foi enviado a moderação do hub para avaliação."
    errors:
      hubMessageOnly: "{x_icon} Você só pode reportar mensgens que foram enviadas a partir dos hubs do InterChat."
      cannotReportSelf: "{x_icon} Você não pode reportar suas próprias mensagens."
  hubCreate:
    success:
      description: "Seu hub **{hubName}** está pronto! Aqui está o que você pode fazer em seguida:"
    errors:
      hubCreationFailed: "Algo deu errado enquanto seu hub era criado, Por favor tente novamente."
      troubleshooting: "O que você pode fazer:"
      troubleshootingSteps: "• Verifique se a URL da sua logo é válida\n• Tente novamente em alguns momentos\n• Contate o suporte se isso persistir"
  general:
    invite:
      title: "Ei!"
      description: "Obrigado por escolher o InterChat. Precisa de ajuda? Entre em nosso servidor de suporte. Use os botões abaixo para convidar nosso bot para seu servidor e começar a conectar servidores."
  stats:
    title: "Estatísticas do InterChat"
    shard:
      title: "Informações do Shard"
      statusReady: "Pronto"
      statusProvisioning: "Provisionando..."
      current: "Shard atual: #{id}"
  help:
    title: "Comandos do InterChat"
    description: "Explore nossa grande variedades de comandos para ajudar você, e sua comunidade conectar com outros."
    noDescription: "Sem descrição"
    name: "Ajuda"
  staff:
    blacklist:
      list:
        description: "ver"
    get:
      description: "Receba ajuda sobre as entidades do InterChat"
      server:
        description: "Receba informações detalhadas sobre um servidor"
      hub:
        description: "Receba informações detalhadas sobre um hub"
      user:
        description: "Receba informações detalhadas sobre um usuário"
  profile:
    achievements:
      noneFound: "Nenhum encontrado"
      noneFoundDescription: "Eu não consegui encontrar nenhuma conquista para este usuário!"
    badges:
      noneFound: "Nenhum encontrado"
      noneFoundDescription: "Eu não consegui encontrar nenhuma insignia para este usuário!"
  leaderboard:
    staffTag: "Moderação do InterChat"
    userTag: "Usuário"
    messagesColumn: "Mensagens"
    voteCountColumn: "Número de votos"
  my:
    hubs:
      title: "Seus hubs"
      description: "Aqui estão os hubs que você adquire ou modera:"
      position: "Posição:"
      owner: "Dono"
  appeal:
    title: "Suas infrações apeláveis"
    description: "Selecione uma infração abaixo para enviar um apelo, ou ver o progresso de apelos existentes."
    noInfractions:
      title: "Sem infrações ativas"
      description: "Você não tem infrações ativas que podem ser apeladas"
    notAppealable: "Essa infração não é mais apelável."
  mod:
    panel:
      description: "Abre o painel de moderação para usuários, mensagens ou servidores"
      contextMenu: "Painel de Moderação"
    ban:
      description: "Bane um usuário ou servidor de um hub."
    mute:
      description: "Silencia um usuário ou servidor de um hub."
    warn:
      description: "Adverte um usuário ou servidor de um hub."
    unmute:
      description: "Revoga infrações de silenciamento ativos para um usuário ou servidor em um hub."
    unban:
      description: "Revoga infrações de banimento ativos para um usuário ou servidor em um hub."
    delete:
      description: "Exclua uma mensagem de InterChat usando um link de mensagem do Discord."
    delete_infraction:
      description: "Deleta uma infração (Apenas para gerentes ou maior.)"
  infractions:
    description: "Veja infrações em um hub, filtrado por usuário ou servidor."
  rules:
    description: "Veja as regras de um hub."
    title: "Regras de {hubName}"
    noRules:
      title: "Sem regras definidas"
      description: "Nenhuma regra foi definida para este hub por agora."
    footer: "{count} regra{plural} • Siga essas regras para manter uma comunidade positiva"
    errors:
      noHub: "Esse comando deve ser usado em um canal do servidor conectado a um hub, ou você deve especificar o nome do hub."
      notConnected: "Esse canal não está conectado a nenhum hub. Por favor, especifique um nome de um hub ou use esse comando em um canal conectado."
      hubNotFound: "Hub \"{hubName}\" não encontrado."
  connections:
    title: "Gestão da conexão"
    description: "Configure e administre todas as conexões de hubs com esse servidor."
    fields:
      lastActive: "Ultima vez ativo"
    selected:
      description: "Você pode agora editar essa conexão. Use o menu abaixo para mudar o canal de transmissão ou editar configurações relacionadas a conexão."
      fields:
        broadcastChannel: 'Transmitindo a'
        connectionState: 'Estado da transmissão'
        lastActive: 'Última vez ativo'
      state:
        enabled: 'Transmissões agora estão **ativas**.'
        disabled: 'Transmissões agora estão **pausadas**.'
    fix:
      title: 'Validação de conexão'
      description: 'Todas as conexões com essa guilda foram validadas. Veja abaixo para mais informações.'
      responses:
        success:
          fixed: 'Conexão concertada!'
          valid: 'Nenhum problema encontrado.'
        errors:
          channelDeleted: 'O canal associado com esse hub foi deletado, Conexão removida.'
          permissionsWebhook: 'Não consigo acessar aos webhooks, por favor reveja minhas permissões e tente novamente.'
          permissionsView: 'Não consigo ver os canais, por favor reveja minhas permissões e tente novamente.'
          permissionsSend: 'Não consigo enviar mensagens, por favor reveja minhas permissões e tente novamente.'

