from datetime import datetime, timed<PERSON>ta
from typing import TYPE_CHECKING, Dict, Literal, Optional, cast

import discord
from sqlalchemy import select

from utils.modules.core.db.models import Hub
from utils.modules.core.i18n import t
from utils.modules.hub.constants import HubPermissionLevel
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.views.hubConfig.utils import (
    BaseHubView,
    DatabaseService,
    DatabaseUtils,
    EmbedFactory,
    HubUpdateFields,
    handle_modal_edit,
)

if TYPE_CHECKING:
    from main import Bot
    from utils.modules.ui.views.hubConfig.hubConfigurationView import ConfigurationView

HUB_NOT_FOUND_MSG = 'Hub not found. Please try again later.'


class GeneralSettingsView(BaseHubView):
    def __init__(
        self,
        bot: 'Bot',
        user: discord.User | discord.Member,
        hub: <PERSON>b,
        permission: HubPermissionLevel,
        locale: str,
        parent_view: Optional['ConfigurationView'] = None,
    ):
        super().__init__(bot, user, hub, permission, locale)
        self.parent_view = parent_view
        self._setup_select_menu()
        self.add_back_button(self.parent_view)
        self.add_dashboard_button()

    async def get_hub(self) -> Optional[Hub]:
        return await DatabaseUtils.get_hub(self.hub.id)

    def _setup_select_menu(self):
        emoji_map = {
            'edit_description': self.bot.emotes.edit_icon,
            'edit_name': self.bot.emotes.hash_icon,
            'edit_welcome': self.bot.emotes.wand_icon,
            'toggle_nsfw': self.bot.emotes.alert_icon,
            'toggle_private': self.bot.emotes.lock_icon,
        }

        for item in self.children:
            if isinstance(item, discord.ui.Select) and hasattr(item, 'options'):
                for option in item.options:
                    if option.value in emoji_map:
                        option.emoji = emoji_map[option.value]

        self.settings_select.placeholder = t('ui.hubConfig.general.placeholder', locale=self.locale)
        self.settings_select.options = [
            discord.SelectOption(
                label=t('ui.hubConfig.general.editDescription.label', locale=self.locale),
                description=t(
                    'ui.hubConfig.general.editDescription.description', locale=self.locale
                ),
                value='edit_description',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.editName.label', locale=self.locale),
                description=t('ui.hubConfig.general.editName.description', locale=self.locale),
                value='edit_name',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.welcomeMessage.label', locale=self.locale),
                description=t(
                    'ui.hubConfig.general.welcomeMessage.description', locale=self.locale
                ),
                value='edit_welcome',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.toggleNsfw.label', locale=self.locale),
                description=t('ui.hubConfig.general.toggleNsfw.description', locale=self.locale),
                value='toggle_nsfw',
            ),
            discord.SelectOption(
                label=t('ui.hubConfig.general.togglePrivate.label', locale=self.locale),
                description=t('ui.hubConfig.general.togglePrivate.description', locale=self.locale),
                value='toggle_private',
            ),
        ]

    # region Helper Methods
    async def _update_and_refresh_ui(
        self, interaction: discord.Interaction, updates: 'HubUpdateFields'
    ) -> bool:
        success = await DatabaseService.update_hub(self.bot, self.hub.id, updates)
        if not success:
            await interaction.followup.send(
                'Failed to update hub settings. Please try again.', ephemeral=True
            )
            return False

        fresh_hub = await self.get_hub()
        if fresh_hub:
            self.hub = fresh_hub

        updated_embed = await self.create_general_settings_embed(fresh_hub)
        self._setup_select_menu()

        try:
            await interaction.edit_original_response(embed=updated_embed, view=self)
        except discord.NotFound:
            pass

        return True

    async def _handle_toggle(
        self,
        interaction: discord.Interaction['Bot'],
        attribute: Literal['nsfw', 'private'],
        title: str,
        status_map: Dict[bool, str],
        details_map: Dict[bool, Dict[str, str]],
    ):
        db_hub = await self.get_hub()
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        current_status = getattr(db_hub, attribute, False)
        new_status = not current_status
        updates: Dict[Literal['nsfw', 'private'], bool] = {attribute: new_status}

        if await self._update_and_refresh_ui(interaction, cast('HubUpdateFields', updates)):
            status_text = status_map[new_status]
            success_embed = EmbedFactory.success(
                self.bot, title, f'Your hub is now: **{status_text}**'
            )
            details = details_map[new_status]
            success_embed.add_field(name=details['name'], value=details['value'], inline=False)
            await interaction.followup.send(embed=success_embed, ephemeral=True)

    # endregion

    # region Embed Creation
    async def create_general_settings_embed(self, fresh_hub: Optional[Hub] = None) -> discord.Embed:
        db_hub = fresh_hub or await self.get_hub()
        if not db_hub:
            return EmbedFactory.error(self.bot, 'Error', HUB_NOT_FOUND_MSG)

        self.hub = db_hub

        embed = discord.Embed(
            color=self.constants.color,
            title='General Settings',
            description=f"A summary of your hub's general settings. For more options, visit the [dashboard](https://www.interchat.tech/dashboard/hubs/{db_hub.id}).",
        )

        # --- Hub Identity ---
        description_preview = (
            db_hub.description[:80] + '...'
            if db_hub.description and len(db_hub.description) > 80
            else db_hub.description or '*No description set.*'
        )
        embed.add_field(
            name=f'{self.bot.emotes.house_icon} Hub Identity',
            value=(
                f'**Name:** {db_hub.name}\n'
                f'**Description:** {description_preview}\n'
                f'**[Edit on Dashboard](https://www.interchat.tech/dashboard/hubs/{db_hub.id})**'
            ),
            inline=False,
        )

        # --- Hub Properties ---
        nsfw_status = 'NSFW' if db_hub.nsfw else 'Family Friendly'
        private_status = 'Private' if db_hub.private else 'Public'
        embed.add_field(
            name='Properties',
            value=(
                f'{self.bot.emotes.alert_icon if db_hub.nsfw else self.bot.emotes.tick} **Rating:** {nsfw_status}\n'
                f'{self.bot.emotes.lock_icon if db_hub.private else self.bot.emotes.globe_icon} **Visibility:** {private_status}'
            ),
            inline=True,
        )

        # --- Welcome Message ---
        welcome_preview = (
            '*No welcome message set.*'
            if not db_hub.welcomeMessage
            else (
                f'"{db_hub.welcomeMessage[:45]}..."'
                if len(db_hub.welcomeMessage) > 45
                else f'"{db_hub.welcomeMessage}"'
            )
        )

        embed.add_field(
            name='Welcome Message',
            value=f'{welcome_preview}',
            inline=True,
        )

        last_updated = (
            db_hub.updatedAt.strftime('%b %d, %Y at %I:%M %p') if db_hub.updatedAt else 'Never'
        )
        embed.set_footer(
            text=f'Last Updated: {last_updated}',
            icon_url=db_hub.iconUrl
            or (self.bot.user.display_avatar.url if self.bot.user else None),
        )

        return embed

    # endregion

    # region UI Component Callbacks
    @discord.ui.select(row=0)
    async def settings_select(
        self, interaction: discord.Interaction['Bot'], select: discord.ui.Select
    ):
        if not await self.interaction_check(interaction):
            return

        action_map = {
            'edit_description': self.edit_description,
            'edit_name': self.edit_name,
            'edit_welcome': self.edit_welcome,
            'toggle_nsfw': self.handle_toggle_nsfw,
            'toggle_private': self.handle_toggle_private,
        }
        selected_action = action_map.get(select.values[0])
        if selected_action:
            await selected_action(interaction)

    # endregion

    # region Action Handlers
    async def edit_description(self, interaction: discord.Interaction['Bot']):
        db_hub = await self.get_hub()
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        modal_config = [
            (
                'description',
                discord.ui.TextInput(
                    label='Hub Description',
                    placeholder='Describe what your hub is about...',
                    style=discord.TextStyle.paragraph,
                    max_length=1000,
                    required=True,
                    default=db_hub.description or '',
                ),
            ),
        ]

        async def update_callback(modal: CustomModal, hub: Hub):
            new_description = modal.saved_items['description'].value.strip()
            if not new_description:
                return False

            # Defer the response in the callback before the update
            return await self._update_and_refresh_ui(interaction, {'description': new_description})

        await handle_modal_edit(
            interaction,
            self.bot,
            self.hub.id,
            'Edit Hub Description',
            modal_config,
            update_callback,
            'Description Updated!',
            'Your hub description has been successfully updated.',
        )

    async def edit_name(self, interaction: discord.Interaction['Bot']):
        db_hub = await self.get_hub()
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        # Cooldown Check
        if hasattr(db_hub, 'lastNameChange') and db_hub.lastNameChange:
            time_since_change = datetime.now() - db_hub.lastNameChange
            if time_since_change < timedelta(days=10):
                days_remaining = 10 - time_since_change.days
                embed = EmbedFactory.error(
                    self.bot,
                    'Name Change on Cooldown',
                    f'You can change your hub name again in **{days_remaining} days**.',
                )
                return await interaction.response.send_message(embed=embed, ephemeral=True)

        # Modal Interaction
        modal = CustomModal(
            'Edit Hub Name',
            [
                (
                    'name',
                    discord.ui.TextInput(
                        label='Hub Name',
                        placeholder='Enter a new name for your hub...',
                        max_length=50,
                        required=True,
                        default=db_hub.name,
                    ),
                ),
            ],
        )
        await interaction.response.send_modal(modal)
        if not await modal.wait():
            return

        new_name = modal.saved_items['name'].value.strip()

        # Validation
        if not new_name:
            embed = EmbedFactory.error(self.bot, 'Invalid Name', 'Hub name cannot be empty.')
            return await interaction.followup.send(embed=embed, ephemeral=True)

        async with self.bot.db.get_session() as session:
            existing_hub = await session.scalar(
                select(Hub).where((Hub.name == new_name) & (Hub.id != self.hub.id))
            )
            if existing_hub:
                embed = EmbedFactory.error(
                    self.bot,
                    'Name Already Taken',
                    f'The name **{new_name}** is already used. Please choose another.',
                )
                return await interaction.followup.send(embed=embed, ephemeral=True)

        # Perform Update and Refresh
        updates: HubUpdateFields = {'name': new_name}
        if hasattr(db_hub, 'lastNameChange'):
            updates['lastNameChange'] = datetime.now()

        await interaction.response.defer()
        if await self._update_and_refresh_ui(interaction, updates):
            success_embed = EmbedFactory.success(
                self.bot,
                'Name Updated!',
                f'Your hub has been renamed to **{new_name}**.',
            )
            success_embed.add_field(
                name='Note',
                value='⚠️ Hub names can only be changed once every 10 days.',
                inline=False,
            )
            await interaction.followup.send(embed=success_embed, ephemeral=True)

    async def edit_welcome(self, interaction: discord.Interaction['Bot']):
        db_hub = await self.get_hub()
        if not db_hub:
            return await interaction.response.send_message(HUB_NOT_FOUND_MSG, ephemeral=True)

        modal_config = [
            (
                'welcome_message',
                discord.ui.TextInput(
                    label='Welcome Message',
                    placeholder='Welcome! Enjoy your stay...',
                    style=discord.TextStyle.paragraph,
                    max_length=2000,
                    required=False,
                    default=db_hub.welcomeMessage or '',
                ),
            ),
        ]

        async def update_callback(modal: CustomModal, hub: Hub):
            welcome_value = modal.saved_items['welcome_message'].value.strip()
            new_welcome = welcome_value if welcome_value else None
            return await self._update_and_refresh_ui(interaction, {'welcomeMessage': new_welcome})

        await handle_modal_edit(
            interaction,
            self.bot,
            self.hub.id,
            'Edit Welcome Message',
            modal_config,
            update_callback,
            'Welcome Message Updated!',
            'Your hub welcome message has been successfully updated.',
        )

    async def handle_toggle_nsfw(self, interaction: discord.Interaction['Bot']):
        await self._handle_toggle(
            interaction,
            attribute='nsfw',
            title='NSFW Setting Updated!',
            status_map={True: '🔞 NSFW', False: '✅ Safe for Work'},
            details_map={
                True: {
                    'name': '⚠️ Important',
                    'value': 'Your hub is now marked as NSFW and may be filtered from searches.',
                },
                False: {
                    'name': '✅ Family Friendly',
                    'value': 'Your hub is now marked as safe for work and visible to all users.',
                },
            },
        )

    async def handle_toggle_private(self, interaction: discord.Interaction['Bot']):
        await self._handle_toggle(
            interaction,
            attribute='private',
            title='Visibility Updated!',
            status_map={True: '🔒 Private', False: '🌐 Public'},
            details_map={
                True: {
                    'name': '🔒 Private Hub',
                    'value': 'Your hub is now private, invite-only, and hidden from public searches.',
                },
                False: {
                    'name': '🌐 Public Hub',
                    'value': 'Your hub is now public and discoverable by all users.',
                },
            },
        )

    # endregion
